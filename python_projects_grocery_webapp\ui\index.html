
<!DOCTYPE html>
<html>
    <head>
        <title>Grocery Store Management System</title>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0">
        <meta name="apple-mobile-web-app-capable" content="yes"/>
        <meta name="csrf-token" content="kmapods5wQ5L1hn7rcR9OPst7EsN0gC7SrHh3m9K"/>

        <!-- Enhanced Font and Icon Libraries -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/material-design-iconic-font/2.2.0/css/material-design-iconic-font.min.css">
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
        <link rel="preconnect" href="https://fonts.googleapis.com">
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

        <!-- Stylesheets -->
        <link media="all" type="text/css" rel="stylesheet" href="css/bootstrap.min.css">
        <link media="all" type="text/css" rel="stylesheet" href="css/style.css?v=1.0">
        <link media="all" type="text/css" rel="stylesheet" href="css/sidebar-menu.css?v=1.0">
        <link media="all" type="text/css" rel="stylesheet" href="css/custom.css?v=1.4.0">

        <style>
            body {
                font-family: 'Inter', sans-serif;
            }
        </style>
    </head>
    <body class="tooltips">
        <div class="container-fluid">
            <!-- Enhanced Header -->
            <div class="enhanced-header">
                <div class="container">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <h1 class="mb-0" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; font-weight: 700; font-size: 1.8rem;">
                                <i class="fas fa-store mr-2"></i>
                                Grocery Store Management
                            </h1>
                        </div>
                        <div class="col-md-6">
                            <div class="modern-nav">
                                <ul class="breadcrumb justify-content-end">
                                    <li>
                                        <a href="index.html" data-toggle="tooltip" data-placement="bottom" title="Dashboard">
                                            <i class="fas fa-chart-line"></i>
                                        </a>
                                    </li>
                                    <li>
                                        <a href="manage-product.html" data-toggle="tooltip" data-placement="bottom" title="Manage Products">
                                            <i class="fas fa-boxes"></i>
                                        </a>
                                    </li>
                                    <li>
                                        <a href="order.html" data-toggle="tooltip" data-placement="bottom" title="New Order">
                                            <i class="fas fa-shopping-cart"></i>
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Enhanced Main Content -->
            <div class="content-modern">
                <div class="container">
                    <!-- Statistics Cards Row -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="stats-card">
                                <div class="icon">
                                    <i class="fas fa-shopping-cart"></i>
                                </div>
                                <div class="number" id="totalOrders">0</div>
                                <div class="label">Total Orders</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card">
                                <div class="icon">
                                    <i class="fas fa-boxes"></i>
                                </div>
                                <div class="number" id="totalProducts">0</div>
                                <div class="label">Products</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card">
                                <div class="icon">
                                    <i class="fas fa-dollar-sign"></i>
                                </div>
                                <div class="number" id="totalRevenue">₹0</div>
                                <div class="label">Total Revenue</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card">
                                <div class="icon">
                                    <i class="fas fa-users"></i>
                                </div>
                                <div class="number" id="totalCustomers">0</div>
                                <div class="label">Customers</div>
                            </div>
                        </div>
                    </div>

                    <!-- Main Dashboard Content -->
                    <div class="row">
                        <div class="col-12">
                            <div class="box-info modern" id="taskFormContainer">
                                <div class="d-flex justify-content-between align-items-center mb-4">
                                    <h2 class="mb-0">
                                        <i class="fas fa-list-alt mr-2"></i>
                                        Recent Orders
                                    </h2>
                                    <div class="action-buttons">
                                        <a href="order.html" class="btn btn-modern btn-icon">
                                            <i class="fas fa-plus"></i>
                                            New Order
                                        </a>
                                        <a href="manage-product.html" class="btn btn-modern btn-success btn-icon">
                                            <i class="fas fa-cog"></i>
                                            Manage Products
                                        </a>
                                    </div>
                                </div>

                                <div class="table-responsive">
                                    <table class="table table-modern">
                                        <thead>
                                            <tr>
                                                <th><i class="fas fa-calendar mr-2"></i>Date</th>
                                                <th><i class="fas fa-hashtag mr-2"></i>Order Number</th>
                                                <th><i class="fas fa-user mr-2"></i>Customer Name</th>
                                                <th><i class="fas fa-rupee-sign mr-2"></i>Total Cost</th>
                                                <th><i class="fas fa-cogs mr-2"></i>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody id="ordersTableBody">
                                            <!-- Orders will be populated here -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Enhanced Loading Modal -->
            <div class="modal fade" id="loadingModal" role="dialog" data-backdrop="static">
                <div class="modal-dialog modal-sm">
                    <div class="modal-content" style="border: none; background: transparent; box-shadow: none;">
                        <div class="modal-body text-center" style="background: transparent;">
                            <div class="loading-spinner"></div>
                            <p class="mt-3" style="color: white; font-weight: 600;">Loading...</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Enhanced User Profile Modal -->
            <div class="modal fade modal-modern" id="userProfileModal" role="dialog">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h4 class="modal-title">
                                <i class="fas fa-user mr-2"></i>
                                User Profile
                            </h4>
                            <button type="button" class="close" data-dismiss="modal" style="color: white;">
                                <span>&times;</span>
                            </button>
                        </div>
                        <div class="modal-body">
                            <!-- Profile content will be loaded here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Enhanced Scripts -->
        <script src="js/packages/jquery.min.js"></script>
        <script src="js/packages/bootstrap.min.js"></script>
        <script src="js/custom/common.js"></script>
        <script src="js/custom/dashboard.js"></script>

        <!-- Enhanced Dashboard Script -->
        <script>
            $(document).ready(function() {
                // Initialize tooltips
                $('[data-toggle="tooltip"]').tooltip();

                // Add smooth scrolling
                $('a[href^="#"]').on('click', function(event) {
                    var target = $(this.getAttribute('href'));
                    if( target.length ) {
                        event.preventDefault();
                        $('html, body').stop().animate({
                            scrollTop: target.offset().top - 100
                        }, 1000);
                    }
                });

                // Add loading animation for buttons
                $('.btn-modern').on('click', function() {
                    var $btn = $(this);
                    var originalText = $btn.html();
                    $btn.html('<i class="fas fa-spinner fa-spin mr-2"></i>Loading...');

                    setTimeout(function() {
                        $btn.html(originalText);
                    }, 2000);
                });
            });
        </script>
    </body>
</html>
